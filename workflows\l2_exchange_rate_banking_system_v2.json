{"id": "l2_exchange_rate_banking_system_v2", "version": "1.0", "pipeline": [{"step": "filler_tts", "process": "filler_tts_process", "agent": "filler_tts_agent", "input": {}, "tools": {"external_tools": "openai"}, "output": {"audio_path": "audio_path", "latencyFillerTTS": "latencyFillerTTS"}}, {"step": "processing", "process": "processing_process", "agent": "processing_agent", "input": {"clean_text": "clean_text", "intent": "intent"}, "tools": {"external_tools": "openai"}, "output": {"llm_answer": "llm_answer", "latencyProcessing": "latencyProcessing"}}, {"step": "tts", "process": "tts_process", "agent": "tts_agent", "input": {"text": "llm_answer", "emotion": "emotion", "gender": "gender"}, "tools": {"external_tools": "openai"}, "output": {"audio_path": "audio_path", "latencyTTS": "latencyTTS"}}], "onInterrupt": {"handler": "interrupt_manager", "resume_from": "stt"}, "onError": {"retry": 2, "fallback_state": "l2_fallback_generic"}}